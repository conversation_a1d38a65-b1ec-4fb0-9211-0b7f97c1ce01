let currentPage = 1;
let totalPages = 1;
let pageSize = 10;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查当前激活的选项卡
    const currentTab = new URLSearchParams(window.location.search).get('tab');
    // 只在切机履历选项卡激活时初始化
    if (currentTab === 'mc') {
        initMachineChangeHistory();
    } else {
        setupTabSwitchListener();
    }
});

// 设置选项卡切换监听器
function setupTabSwitchListener() {
    // 监听选项卡切换事件
    const mcTabButton = document.querySelector('button[onclick="switchTab(\'mc\')"]');
    if (mcTabButton) {
        mcTabButton.addEventListener('click', function() {
            // 延迟初始化，确保选项卡切换完成
            setTimeout(() => {
                if (!window.mcHistoryInitialized) {
                    initMachineChangeHistory();
                    window.mcHistoryInitialized = true;
                }
            }, 100);
        });
    }
}
// 初始化切机履历功能
async function initMachineChangeHistory() {
    try {
        // 检查切机履历选项卡是否存在
        const mcTab = document.getElementById('mcTab');
        const mcTable = document.getElementById('mcHistoryTable');

        if (!mcTab || !mcTable) {
            console.warn('切机履历选项卡或表格不存在，跳过初始化');
            return;
        }

        // 加载SITE和EQP选项
        await loadOptions();

        // 绑定事件监听器
        bindEventListeners();

        // 确保表格显示默认提示信息
        resetMachineChangeHistory();

        window.mcHistoryInitialized = true;
    } catch (error) {
        console.error('初始化失败:', error);
        // 只在切机履历选项卡激活时显示错误信息
        const currentTab = new URLSearchParams(window.location.search).get('tab');
        if (currentTab === 'mc') {
            showError('初始化失败: ' + error.message);
        }
    }
}

// 加载SITE和EQP选项
async function loadOptions() {
    try {
        // 检查选择框是否存在
        const siteSelect = document.getElementById('siteSelect');
        const eqpSelect = document.getElementById('eqpSelect');

        if (!siteSelect || !eqpSelect) {
            console.warn('SITE或EQP选择框不存在，跳过选项加载');
            return;
        }

        const response = await fetch('get_options.php');
        const result = await response.json();

        if (result.success) {
            const { sites, eqps } = result.data;

            // 填充SITE选项
            siteSelect.innerHTML = '<option value="">请选择SITE</option>';
            sites.forEach(site => {
                const option = document.createElement('option');
                option.value = site;
                option.textContent = site;
                siteSelect.appendChild(option);
            });

            // 填充EQP选项
            eqpSelect.innerHTML = '<option value="">请选择EQP</option>';
            eqps.forEach(eqp => {
                const option = document.createElement('option');
                option.value = eqp;
                option.textContent = eqp;
                eqpSelect.appendChild(option);
            });
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('加载选项失败:', error);
        // 只在切机履历选项卡激活时显示错误信息
        const currentTab = new URLSearchParams(window.location.search).get('tab');
        if (currentTab === 'mc') {
            showError('加载选项失败: ' + error.message);
        }
    }
}

// 绑定事件监听器
function bindEventListeners() {
    // 查询按钮
    document.getElementById('mcQueryBtn').addEventListener('click', queryMachineChangeHistory);
    
    // 重置按钮
    document.getElementById('mcResetBtn').addEventListener('click', resetMachineChangeHistory);
    
    // 点检登录按钮
    document.getElementById('inspectionLoginBtn').addEventListener('click', openInspectionModal);
    
    // 分页按钮
    document.getElementById('mcFirstBtn').addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage = 1;
            queryMachineChangeHistory();
        }
    });
    
    document.getElementById('mcPrevBtn').addEventListener('click', () => {
        if (currentPage > 1) {
            currentPage--;
            queryMachineChangeHistory();
        }
    });
    
    document.getElementById('mcNextBtn').addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage++;
            queryMachineChangeHistory();
        }
    });
    
    document.getElementById('mcLastBtn').addEventListener('click', () => {
        if (currentPage < totalPages) {
            currentPage = totalPages;
            queryMachineChangeHistory();
        }
    });
    
    // 每页显示条数改变
    document.getElementById('mcPageSizeSelect').addEventListener('change', (e) => {
        pageSize = parseInt(e.target.value);
        currentPage = 1; // 重置到第一页
        queryMachineChangeHistory();
    });
    
    // 页面跳转
    document.getElementById('mcPageJumpBtn').addEventListener('click', jumpToPage);
    
    // 回车跳转页面
    document.getElementById('mcPageJumpInput').addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            jumpToPage();
        }
    });
    
    // PROJECT选择变化事件
    document.getElementById('masterProject').addEventListener('change', updateDetailRows);
}

// 跳转到指定页面
function jumpToPage() {
    const pageInput = document.getElementById('mcPageJumpInput');
    const page = parseInt(pageInput.value);
    
    if (!isNaN(page) && page >= 1 && page <= totalPages) {
        currentPage = page;
        queryMachineChangeHistory();
        pageInput.value = ''; // 清空输入框
    } else {
        alert(`请输入有效的页码 (1-${totalPages})`);
    }
}

// 查询切机履历
async function queryMachineChangeHistory() {
    try {
        const siteid = document.getElementById('siteSelect').value;
        const eqpid = document.getElementById('eqpSelect').value;
        
        // 构建查询参数
        const params = new URLSearchParams({
            page: currentPage,
            page_size: pageSize
        });
        
        if (siteid) params.append('siteid', siteid);
        if (eqpid) params.append('eqpid', eqpid);
        
        // 显示加载状态
        showLoadingmc();
        
        const response = await fetch(`get_model_change_history.php?${params}`);
        const result = await response.json();
        
        if (result.success) {
            displayMachineChangeHistory(result.data);
            updatePagination(result.pagination);
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('查询失败:', error);
        showError('查询失败: ' + error.message);
    }
}

// 显示切机履历数据
function displayMachineChangeHistory(data) {
    const tbody = document.querySelector('#mcHistoryTable tbody');
    
    if (data.length === 0) {
        tbody.innerHTML = `
            <tr class="empty-row">
                <td colspan="16" style="text-align: center; padding: 20px; color: #999;">
                    未找到匹配的数据
                </td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = data.map(row => {
        // 处理点检表列
        let inspectionCell = '';
        if (row.inspection_details && row.inspection_details.length > 0) {
            inspectionCell = row.inspection_details.map(detail => 
                `<div class="inspection-link" onclick="showInspectionDetail('${detail.check_id}')">${detail.PROJECT}</div>`
            ).join('');
        } else {
            inspectionCell = '-';
        }
        
        return `
            <tr>
                <td>${row.eqp_id || '-'}</td>
                <td>${row.date || '-'}</td>
                <td>${row.shift1 || '-'}</td>
                <td>${row.shift2 || '-'}</td>
                <td>${row.mc_time || '-'}</td>
                <td>${row.mc_times || '-'}</td>
                <td>${row.start_time1 || '-'}</td>
                <td>${row.start_time2 || '-'}</td>
                <td>${row.s_time || '-'}</td>
                <td>${row.mc_type || '-'}</td>
                <td>${row.noplan || '-'}</td>
                <td>${row.old_model || '-'}</td>
                <td>${row.track_product || '-'}</td>
                <td>${row.abaflag || '-'}</td>
                <td>${row.prod_type || '-'}</td>
                <td>${inspectionCell}</td>
            </tr>
        `;
    }).join('');
}

// 更新分页信息
function updatePagination(pagination) {
    currentPage = pagination.current_page;
    totalPages = pagination.total_pages;
    
    const pageInfo = document.getElementById('mcPageInfo');
    const prevBtn = document.getElementById('mcPrevBtn');
    const nextBtn = document.getElementById('mcNextBtn');
    const firstBtn = document.getElementById('mcFirstBtn');
    const lastBtn = document.getElementById('mcLastBtn');
    const paginationContainer = document.getElementById('mcPagination');
    const pageSizeSelect = document.getElementById('mcPageSizeSelect');
    
    pageInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页 (总计 ${pagination.total} 条)`;
    
    prevBtn.disabled = currentPage <= 1;
    nextBtn.disabled = currentPage >= totalPages;
    firstBtn.disabled = currentPage <= 1;
    lastBtn.disabled = currentPage >= totalPages;
    
    // 设置当前每页显示条数
    pageSizeSelect.value = pagination.page_size;
    
    // 显示分页控件
    paginationContainer.style.display = totalPages > 1 ? 'block' : 'none';
}

// 重置查询条件
function resetMachineChangeHistory() {
    document.getElementById('siteSelect').value = '';
    document.getElementById('eqpSelect').value = '';
    currentPage = 1;
    
    // 重置表格显示
    const tbody = document.querySelector('#mcHistoryTable tbody');
    tbody.innerHTML = `
        <tr class="default-message">
            <td colspan="16" style="text-align: center; padding: 20px; color: #999; font-style: italic;">
                请点击查询按钮获取切机履历
            </td>
        </tr>
    `;
    
    // 隐藏分页控件
    document.getElementById('mcPagination').style.display = 'none';
}

// 显示加载状态
function showLoadingmc() {
    const tbody = document.querySelector('#mcHistoryTable tbody');
    tbody.innerHTML = `
        <tr class="loading-row">
            <td colspan="7" style="text-align: center; padding: 20px;">
                <div class="loading-spinner"></div>
                <span style="margin-left: 10px;">加载中...</span>
            </td>
        </tr>
    `;
}

// 显示错误信息
function showError(message) {
    const tbody = document.querySelector('#mcHistoryTable tbody');
    tbody.innerHTML = `
        <tr class="error-row">
            <td colspan="7" style="text-align: center; padding: 20px; color: #ff4d4f;">
                ${message}
            </td>
        </tr>
    `;
}

// 打开点检登录模态框
function openInspectionModal() {
    const modal = document.getElementById('inspectionModal');
    modal.style.display = 'block';
    
    // 初始化表单
    resetInspectionForm();
    
    // 设置默认值
    const siteSelect = document.getElementById('siteSelect');
    const eqpSelect = document.getElementById('eqpSelect');
    
    if (siteSelect.value) {
        document.getElementById('masterSiteId').value = siteSelect.value;
    }
    if (eqpSelect.value) {
        document.getElementById('masterEqpId').value = eqpSelect.value;
    }
    
    // 设置当前日期
    document.getElementById('masterMcDate').value = new Date().toISOString().split('T')[0];
    
    // 初始化明细表行数
    updateDetailRows();
}

// 关闭点检登录模态框
function closeInspectionModal() {
    document.getElementById('inspectionModal').style.display = 'none';
}

// 重置点检表单
function resetInspectionForm() {
    // 重置主表表单
    document.getElementById('masterSiteId').value = '';
    document.getElementById('masterEqpId').value = '';
    document.getElementById('masterMcDate').value = '';
    document.getElementById('masterOldProduct').value = '';
    document.getElementById('masterTrackProduct').value = '';
    document.getElementById('masterProject').value = '';
    document.getElementById('masterMcOperator').value = '';
    document.getElementById('masterInspector').value = '';
}

// 根据PROJECT选择更新明细表行数
function updateDetailRows() {
    const project = document.getElementById('masterProject').value;
    const tbody = document.getElementById('detailTableBody');

    // 清空现有行
    tbody.innerHTML = '';

    if (project === 'CP') {
        // CP项目：4行
        generateDetailRows(checkItemsConfig.CP, tbody);
    } else if (project === 'OLB') {
        // OLB项目：30行
        generateDetailRows(checkItemsConfig.OLB, tbody);
    } else {
        // 默认情况：显示"请选择工程"
        const row = document.createElement('tr');
        row.innerHTML = `
            <td colspan="10" style="text-align: center; padding: 20px; color: #999; font-style: italic;">请选择工程</td>
        `;
        tbody.appendChild(row);
    }
}

// 生成明细表行的通用函数
function generateDetailRows(configData, tbody) {
    configData.forEach(item => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><input type="text" class="detail-input ${item.readonly.includes('item-no') ? 'readonly-input' : 'editable-input'}" value="${item.itemNo}" ${item.readonly.includes('item-no') ? 'readonly' : ''}></td>
            <td><input type="text" class="detail-input ${item.readonly.includes('CHECK_CONTENT') ? 'readonly-input' : 'editable-input'}" value="${item.checkContent}" ${item.readonly.includes('CHECK_CONTENT') ? 'readonly' : ''}></td>
            <td><input type="text" class="detail-input ${item.readonly.includes('METHOD') ? 'readonly-input' : 'editable-input'}" value="${item.method}" ${item.readonly.includes('METHOD') ? 'readonly' : ''}></td>
            <td><input type="text" class="detail-input ${item.readonly.includes('SPEC_LSL') ? 'readonly-input' : 'editable-input'}" value="${item.specLsl}" ${item.readonly.includes('SPEC_LSL') ? 'readonly' : ''}></td>
            <td><input type="text" class="detail-input ${item.readonly.includes('SPEC_TAR') ? 'readonly-input' : 'editable-input'}" value="${item.specTar}" ${item.readonly.includes('SPEC_TAR') ? 'readonly' : ''}></td>
            <td><input type="text" class="detail-input ${item.readonly.includes('SPEC_USL') ? 'readonly-input' : 'editable-input'}" value="${item.specUsl}" ${item.readonly.includes('SPEC_USL') ? 'readonly' : ''}></td>
            <td><input type="text" class="detail-input ${item.readonly.includes('PIC_OK') ? 'readonly-input' : 'editable-input'}" value="" ${item.readonly.includes('PIC_OK') ? 'readonly' : ''}></td>
            <td><input type="text" class="detail-input ${item.readonly.includes('PIC_NG') ? 'readonly-input' : 'editable-input'}" value="" ${item.readonly.includes('PIC_NG') ? 'readonly' : ''}></td>
            <td><input type="text" class="detail-input editable-input" value=""></td>
            <td><input type="text" class="detail-input editable-input" value=""></td>
        `;
        tbody.appendChild(row);
    });
}

// 提交点检数据
async function submitInspection() {
    try {
        // 验证必填字段 - 移除BEFORE_PRODUCT，新增MC_OPERATOR
        const requiredFields = [
            { id: 'masterSiteId', name: 'SITEID' },
            { id: 'masterEqpId', name: 'EQPID' },
            { id: 'masterMcDate', name: 'MC_DATE' },
            { id: 'masterOldProduct', name: 'OLD_PRODUCT' },
            { id: 'masterTrackProduct', name: 'TRACK_PRODUCT' },
            { id: 'masterProject', name: 'PROJECT' },
            { id: 'masterMcOperator', name: 'MC_OPERATOR' },
            { id: 'masterInspector', name: 'INSPECTOR' }
        ];

        for (const field of requiredFields) {
            const value = document.getElementById(field.id).value.trim();
            if (!value) {
                alert(`请填写${field.name}`);
                document.getElementById(field.id).focus();
                return;
            }
        }

        // 收集主表数据 - 使用新的字段结构
        const masterData = {
            SITEID: document.getElementById('masterSiteId').value.trim(),
            EQPID: document.getElementById('masterEqpId').value.trim(),
            MC_DATE: document.getElementById('masterMcDate').value,
            OLD_PRODUCT: document.getElementById('masterOldProduct').value.trim(),
            TRACK_PRODUCT: document.getElementById('masterTrackProduct').value.trim(),
            PROJECT: document.getElementById('masterProject').value,
            MC_OPERATOR: document.getElementById('masterMcOperator').value.trim(),
            INSPECTOR: document.getElementById('masterInspector').value.trim()
        };

        // 收集明细表数据
        const detailRows = document.querySelectorAll('#detailTableBody tr');
        const details = [];

        detailRows.forEach((row) => {
            // 跳过"请选择工程"的提示行
            if (row.cells.length === 1) {
                return;
            }

            const inputs = row.querySelectorAll('.detail-input');
            if (inputs.length >= 10) {
                const detail = {
                    ITEM_NO: inputs[0].value.trim(),
                    CHECK_CONTENT: inputs[1].value.trim(),
                    METHOD: inputs[2].value.trim(),
                    SPEC_LSL: inputs[3].value.trim(),
                    SPEC_TAR: inputs[4].value.trim(),
                    SPEC_USL: inputs[5].value.trim(),
                    PIC_OK: inputs[6].value.trim(),
                    PIC_NG: inputs[7].value.trim(),
                    MEASURE: inputs[8].value.trim(),
                    JUDGEMENT: inputs[9].value.trim()
                };

                // 只添加非空行
                if (detail.ITEM_NO || detail.CHECK_CONTENT) {
                    details.push(detail);
                }
            }
        });

        // 准备提交数据
        const submitData = {
            ...masterData,
            details: details
        };

        // 提交数据
        const response = await fetch('submit_inspection.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(submitData)
        });

        const result = await response.json();

        if (result.success) {
            alert('点检数据提交成功！');
            closeInspectionModal();

            // 重新查询数据
            queryMachineChangeHistory();
        } else {
            throw new Error(result.message);
        }

    } catch (error) {
        console.error('提交失败:', error);
        alert('提交失败: ' + error.message);
    }
}

// 显示点检详情
async function showInspectionDetail(checkId) {
    try {
        const response = await fetch(`get_inspection_detail.php?check_id=${encodeURIComponent(checkId)}`);
        const result = await response.json();

        if (result.success) {
            const { master, details } = result.data;

            // 显示主表数据 - 使用新的字段结构
            const masterDisplay = document.getElementById('inspectionMasterDisplay');
            masterDisplay.innerHTML = `
                <table class="form-table">
                    <thead>
                        <tr>
                            <th>CHECK_ID</th>
                            <th>SITEID</th>
                            <th>EQPID</th>
                            <th>MC_DATE</th>
                            <th>OLD_PRODUCT</th>
                            <th>TRACK_PRODUCT</th>
                            <th>PROJECT</th>
                            <th>MC_OPERATOR</th>
                            <th>INSPECTOR</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>${master.CHECK_ID}</td>
                            <td>${master.SITEID}</td>
                            <td>${master.EQPID}</td>
                            <td>${master.MC_DATE}</td>
                            <td>${master.OLD_PRODUCT}</td>
                            <td>${master.TRACK_PRODUCT}</td>
                            <td>${master.PROJECT}</td>
                            <td>${master.MC_OPERATOR}</td>
                            <td>${master.INSPECTOR}</td>
                        </tr>
                    </tbody>
                </table>
            `;

            // 显示明细数据
            const detailDisplay = document.getElementById('inspectionDetailDisplay');
            if (details.length > 0) {
                detailDisplay.innerHTML = `
                    <table class="form-table">
                        <thead>
                            <tr>
                                <th>ITEM_NO</th>
                                <th>CHECK_CONTENT</th>
                                <th>METHOD</th>
                                <th>SPEC_LSL</th>
                                <th>SPEC_TAR</th>
                                <th>SPEC_USL</th>
                                <th>PIC_OK</th>
                                <th>PIC_NG</th>
                                <th>MEASURE</th>
                                <th>JUDGEMENT</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${details.map(detail => `
                                <tr>
                                    <td>${detail.ITEM_NO || '-'}</td>
                                    <td>${detail.CHECK_CONTENT || '-'}</td>
                                    <td>${detail.METHOD || '-'}</td>
                                    <td>${detail.SPEC_LSL || '-'}</td>
                                    <td>${detail.SPEC_TAR || '-'}</td>
                                    <td>${detail.SPEC_USL || '-'}</td>
                                    <td>${detail.PIC_OK || '-'}</td>
                                    <td>${detail.PIC_NG || '-'}</td>
                                    <td>${detail.MEASURE || '-'}</td>
                                    <td>${detail.JUDGEMENT || '-'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                `;
            } else {
                detailDisplay.innerHTML = '<p style="text-align: center; color: #999;">暂无明细数据</p>';
            }

            // 显示模态框
            document.getElementById('inspectionDetailModal').style.display = 'block';

        } else {
            throw new Error(result.message);
        }

    } catch (error) {
        console.error('获取点检详情失败:', error);
        alert('获取点检详情失败: ' + error.message);
    }
}

// 关闭点检详情模态框
function closeInspectionDetailModal() {
    document.getElementById('inspectionDetailModal').style.display = 'none';
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const inspectionModal = document.getElementById('inspectionModal');
    const detailModal = document.getElementById('inspectionDetailModal');

    if (event.target === inspectionModal) {
        closeInspectionModal();
    }
    if (event.target === detailModal) {
        closeInspectionDetailModal();
    }
}
