.CUTlist {
    overflow: auto;
}

.CUTlist .data-table {
    font-size: 13px;
}

.CUTlist .data-table th:nth-child(1),
.CUTlist .data-table td:nth-child(1) {
    position: sticky;
    left: 0;
    z-index: 2;
    min-width: 60px;
    background-color: #f5f7fa;
}

.CUTlist .data-table th:nth-child(2),
.CUTlist .data-table td:nth-child(2) {
    position: sticky;
    left: 60px;
    z-index: 2;
    min-width: 55px;
    background-color: #f5f7fa;
}

.CUTlist .data-table th:nth-child(3),
.CUTlist .data-table td:nth-child(3) {
    position: sticky;
    left: 115px;
    z-index: 2;
    min-width: 75px;
}
.CUTlist .data-table th:nth-child(4),
.CUTlist .data-table td:nth-child(4) {
    position: sticky;
    left: 190px;
    z-index: 2;
    min-width: 65px;
}
.CUTlist .data-table th:nth-child(5),
.CUTlist .data-table td:nth-child(5) {
    position: sticky;
    left: 255px;
    z-index: 2;
    min-width: 55px;
}


.CUTlist .data-table th {
    padding: 12px 0px;
    position: sticky;
    top: 0;
    z-index: 2;
    background-color: #f5f7fa;
}

.CUTlist .data-table td:nth-child(1),
.CUTlist .data-table td:nth-child(2),
.CUTlist .data-table td:nth-child(3),
.CUTlist .data-table td:nth-child(4),
.CUTlist .data-table td:nth-child(5) {
    padding: 0px;
}

/* 处理固定行列交叉处的单元格 */
.CUTlist .data-table th:nth-child(1),
.CUTlist .data-table th:nth-child(2),
.CUTlist .data-table th:nth-child(3),
.CUTlist .data-table th:nth-child(4),
.CUTlist .data-table th:nth-child(5){
    z-index: 3;
    /* 确保交叉点的表头单元格在最上层 */
    border-bottom: 1px solid #ddd;
}

.CUTlist .data-table td,
.CUTlist .data-table th {
    min-width: 50px;
    /* padding: 12px; */
    border: 1px solid #ddd;
    text-align: center;
}

.CUTlist .data-table tr:hover {
    background-color: #f9fafb;
}

.inventory-cell {
    background-color: #f0f9eb;
    font-weight: bold;
}

.loading-spinner {
    display: inline-block;
    width: 24px;
    height: 24px;
    border: 3px solid #f3f3f3;
    border-radius: 50%;
    border-top-color: #3498db;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

.highlight {
    background-color: rgb(255, 214, 220);
    font-weight: bold;
    color: red;
}

/* 可点击单元格样式 */
.clickable-cell {
    cursor: pointer;
}

.cut-cell {
    font-weight: bold;
    color: #003366;
    text-decoration: underline;
}

.clickable-cell:hover {
    background-color: #e6f7ff;
}

/* 搜索工具栏样式优化 */
.search-toolbar {
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 12px 16px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.search-toolbar label {
    font-weight: bold;
    color: #333;
    white-space: nowrap;
}

.search-toolbar .ant-input {
    width: 200px;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.search-toolbar .ant-input:focus {
    border-color: #1890ff;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.search-toolbar .ant-btn {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    white-space: nowrap;
}

.search-toolbar .ant-btn-primary {
    background-color: #1890ff;
    color: #fff;
}

.search-toolbar .ant-btn-default {
    background-color: #bfbfbf;
    color: #fff;
}

.search-toolbar .ant-btn:hover {
    opacity: 0.8;
}

.search-toolbar .ant-btn-primary:hover {
    background-color: #40a9ff;
}

.search-toolbar .ant-btn-default:hover {
    background-color: #8c8c8c;
}

/* 添加按钮容器，使按钮右对齐 */
.search-toolbar .button-group {
    margin-left: auto;
    display: flex;
    gap: 8px;
}

/* LINE-MODEL-JIG表格样式 */
.line-model-jig-section {
    background-color: #fafafa;
    border-radius: 4px;
    border: 1px solid #e0e0e0;
}

.line-model-jig-table-container {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.line-model-jig-table {
    font-size: 13px;
    width: 100%;
    border-collapse: collapse;
    background-color: #fff;
}

.line-model-jig-table th {
    background-color: #fafafa;
    border: 1px solid #f0f0f0;
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    color: #262626;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 1;
}

.line-model-jig-table td {
    border: 1px solid #f0f0f0;
    padding: 8px;
    text-align: center;
    color: #595959;
    transition: background-color 0.3s ease;
}

.line-model-jig-table tr:hover td {
    background-color: #f5f5f5;
}

.line-model-jig-table tr:nth-child(even) {
    background-color: #fafafa;
}

.line-model-jig-table tr:nth-child(even):hover td {
    background-color: #f0f0f0;
}

/* 响应式设计 - 小屏幕适配 */
@media screen and (max-width: 768px) {
    .line-model-jig-section {
        padding: 8px;
        margin-top: 12px;
    }

    .line-model-jig-table-container {
        height: 250px;
    }

    .line-model-jig-table th,
    .line-model-jig-table td {
        padding: 6px 4px;
        font-size: 12px;
    }

    .line-model-jig-table th:nth-child(1) { width: 60px; }
    .line-model-jig-table th:nth-child(2) { width: 100px; }
    .line-model-jig-table th:nth-child(3) { width: 80px; }
}

/* 滚动条样式优化 */
.CUTlist::-webkit-scrollbar ,
.line-model-jig-table-container::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.CUTlist::-webkit-scrollbar-track ,
.line-model-jig-table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.CUTlist::-webkit-scrollbar-thumb ,
.line-model-jig-table-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.CUTlist::-webkit-scrollbar-thumb:hover ,
.line-model-jig-table-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 表格加载状态样式 */
.line-model-jig-table .loading-row td {
    text-align: center;
    padding: 20px;
    color: #999;
    font-style: italic;
}

/* 表格空状态样式 */
.line-model-jig-table .empty-row td {
    text-align: center;
    padding: 20px;
    color: #999;
    background-color: #fafafa;
}

/* 表格默认消息样式 */
.line-model-jig-table .default-message td {
    text-align: center;
    padding: 20px;
    color: #999;
    font-style: italic;
    background-color: #fafafa;
}

/* 表格错误状态样式 */
.line-model-jig-table .error-row td {
    text-align: center;
    padding: 20px;
    color: #ff4d4f;
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
}

/* 切机履历表格独立样式 */
.mc-history-table {
    font-size: 13px;
    width: 100%;
    border-collapse: collapse;
    background-color: #fff;
}

.mc-history-table th {
    background-color: #fafafa;
    border: 1px solid #f0f0f0;
    padding: 12px 8px;
    text-align: center;
    font-weight: 600;
    color: #262626;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 1;
}

.mc-history-table td {
    border: 1px solid #f0f0f0;
    padding: 8px;
    text-align: center;
    color: #595959;
    transition: background-color 0.3s ease;
}

.mc-history-table tr:hover td {
    background-color: #f5f5f5;
}

.mc-history-table tr:nth-child(even) {
    background-color: #fafafa;
}

.mc-history-table tr:nth-child(even):hover td {
    background-color: #f0f0f0;
}

/* 切机履历表格状态样式 */
.mc-history-table .loading-row td {
    text-align: center;
    padding: 20px;
    color: #999;
    font-style: italic;
}

.mc-history-table .empty-row td {
    text-align: center;
    padding: 20px;
    color: #999;
    background-color: #fafafa;
}

.mc-history-table .default-message td {
    text-align: center;
    padding: 20px;
    color: #999;
    font-style: italic;
    background-color: #fafafa;
}

.mc-history-table .error-row td {
    text-align: center;
    padding: 20px;
    color: #ff4d4f;
    background-color: #fff2f0;
    border: 1px solid #ffccc7;
}

/* 分页控件样式 */
.pagination-container {
    padding: 16px;
    background-color: #fafafa;
    border-top: 1px solid #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
}

.pagination-container .ant-btn {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #d9d9d9;
}

.pagination-container .ant-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 点检表链接样式 */
.inspection-link {
    color: #003366;
    text-decoration: underline;
    cursor: pointer;
    font-weight: bold;
}

.inspection-link:hover {
    color: #1890ff;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.45);
    backdrop-filter: blur(2px);
}

.modal-content {
    background-color: #ffffff;
    margin: 1% auto;
    padding: 0;
    border: none;
    border-radius: 6px;
    width: 95%;
    max-width: 1400px;
    max-height: 95vh;
    overflow: hidden;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    background-color: #ffffff;
    padding: 16px 24px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 55px;
}

.modal-header h3 {
    margin: 0;
    color: #262626;
    font-size: 16px;
    font-weight: 600;
    line-height: 1.5;
}

.modal-body {
    padding: 24px;
    max-height: calc(95vh - 140px);
    overflow-y: auto;
    background-color: #ffffff;
}

.modal-body h4 {
    color: #262626;
    font-size: 14px;
    font-weight: 600;
    margin: 0 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;
}

.modal-footer {
    background-color: #ffffff;
    padding: 12px 24px 16px;
    border-top: 1px solid #f0f0f0;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
}

.modal-footer .ant-btn {
    height: 32px;
    padding: 4px 15px;
    font-size: 14px;
    border-radius: 6px;
    font-weight: 400;
    line-height: 1.5715;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}

.modal-footer .ant-btn-primary {
    background-color: #1890ff;
    border-color: #1890ff;
    color: #fff;
}

.modal-footer .ant-btn-primary:hover {
    background-color: #40a9ff;
    border-color: #40a9ff;
}

.modal-footer .ant-btn-default {
    background-color: #fff;
    border-color: #d9d9d9;
    color: #000000d9;
}

.modal-footer .ant-btn-default:hover {
    border-color: #40a9ff;
    color: #40a9ff;
}

.close {
    color: #00000073;
    font-size: 22px;
    font-weight: 600;
    cursor: pointer;
    line-height: 1;
    transition: color 0.3s ease;
    padding: 0;
    background: none;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 22px;
    height: 22px;
}

.close:hover,
.close:focus {
    color: #000000d9;
    text-decoration: none;
}

/* 表单表格样式 */
.form-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 16px;
    font-size: 13px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.form-table th,
.form-table td {
    border: 1px solid #e8e8e8;
    padding: 12px 8px;
    text-align: center;
    vertical-align: middle;
    position: relative;
}

.form-table th {
    background-color: #fafafa;
    font-weight: 600;
    color: #333;
    white-space: nowrap;
    font-size: 14px;
    height: 45px;
}

.form-table td {
    background-color: #fff;
    height: 50px;
    /* min-width: 80px; 移除通用最小宽度，避免与明细表列宽冲突 */
}

.form-table input,
.form-table select {
    width: calc(100% - 4px);
    max-width: 100%;
    padding: 6px 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    font-size: 13px;
    line-height: 1.5;
    background-color: #fff;
    transition: all 0.3s ease;
    box-sizing: border-box;
    margin: 0;
}

.form-table input:focus,
.form-table select:focus {
    border-color: #1890ff;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    background-color: #fff;
}

.form-table input:hover,
.form-table select:hover {
    border-color: #40a9ff;
}

/* 特定字段宽度优化 - 仅适用于非明细表的表格 */
.form-table:not(#detailTable .form-table) th:nth-child(1),
.form-table:not(#detailTable .form-table) td:nth-child(1) { min-width: 80px; }  /* SITEID */
.form-table:not(#detailTable .form-table) th:nth-child(2),
.form-table:not(#detailTable .form-table) td:nth-child(2) { min-width: 80px; }  /* EQPID */
.form-table:not(#detailTable .form-table) th:nth-child(3),
.form-table:not(#detailTable .form-table) td:nth-child(3) { min-width: 120px; } /* DATE */
.form-table:not(#detailTable .form-table) th:nth-child(4),
.form-table:not(#detailTable .form-table) td:nth-child(4) { min-width: 120px; } /* BEFORE_PRODUCT */
.form-table:not(#detailTable .form-table) th:nth-child(5),
.form-table:not(#detailTable .form-table) td:nth-child(5) { min-width: 120px; } /* OLD_PRODUCT */
.form-table:not(#detailTable .form-table) th:nth-child(6),
.form-table:not(#detailTable .form-table) td:nth-child(6) { min-width: 120px; } /* TRACK_PRODUCT */
.form-table:not(#detailTable .form-table) th:nth-child(7),
.form-table:not(#detailTable .form-table) td:nth-child(7) { min-width: 80px; }  /* PROJECT */
.form-table:not(#detailTable .form-table) th:nth-child(8),
.form-table:not(#detailTable .form-table) td:nth-child(8) { min-width: 100px; } /* 点检人 */

/* 明细表独立列宽控制 */
#detailTable {
    table-layout: fixed !important; /* 固定表格布局，确保列宽控制生效 */
    width: 100% !important;
}

#detailTable .form-table {
    table-layout: fixed !important; /* 确保表格使用固定布局 */
    width: 100% !important;
    border-collapse: collapse; /* 边框合并 */
}

/* 表格头部样式优化 */
#detailTable .form-table th {
    word-wrap: break-word; /* 标题文字换行 */
    white-space: normal; /* 允许换行 */
    padding: 8px 4px; /* 调整内边距 */
    font-size: 12px; /* 稍微缩小字体 */
    line-height: 1.2; /* 调整行高 */
}

/* 表格单元格内容优化 */
#detailTable .form-table td {
    padding: 4px 2px; /* 减少内边距以节省空间 */
    overflow: hidden; /* 隐藏溢出内容 */
}

/* 各列独立宽度设置 - 使用!important确保优先级 */
#detailTable .form-table th:nth-child(1),
#detailTable .form-table td:nth-child(1) {
    width: 80px !important;
    min-width: 80px !important;
    max-width: 80px !important;
} /* ITEM_NO - 固定宽度 */

#detailTable .form-table th:nth-child(2),
#detailTable .form-table td:nth-child(2) {
    width: 200px !important;
    min-width: 180px !important;
    max-width: 250px !important;
} /* CHECK_CONTENT - 检查内容，需要较宽空间 */

#detailTable .form-table th:nth-child(3),
#detailTable .form-table td:nth-child(3) {
    width: 120px !important;
    min-width: 100px !important;
    max-width: 150px !important;
} /* METHOD - 检查方法 */

#detailTable .form-table th:nth-child(4),
#detailTable .form-table td:nth-child(4) {
    width: 100px !important;
    min-width: 80px !important;
    max-width: 120px !important;
} /* SPEC_LSL - 规格下限 */

#detailTable .form-table th:nth-child(5),
#detailTable .form-table td:nth-child(5) {
    width: 300px !important;
    min-width: 250px !important;
    max-width: 400px !important;
} /* SPEC_TAR - 规格目标值，长文本需要最宽空间 */

#detailTable .form-table th:nth-child(6),
#detailTable .form-table td:nth-child(6) {
    width: 100px !important;
    min-width: 80px !important;
    max-width: 120px !important;
} /* SPEC_USL - 规格上限 */

#detailTable .form-table th:nth-child(7),
#detailTable .form-table td:nth-child(7) {
    width: 100px !important;
    min-width: 80px !important;
    max-width: 120px !important;
} /* PIC_OK - 合格图片 */

#detailTable .form-table th:nth-child(8),
#detailTable .form-table td:nth-child(8) {
    width: 100px !important;
    min-width: 80px !important;
    max-width: 120px !important;
} /* PIC_NG - 不合格图片 */

#detailTable .form-table th:nth-child(9),
#detailTable .form-table td:nth-child(9) {
    width: 120px !important;
    min-width: 100px !important;
    max-width: 150px !important;
} /* MEASURE - 测量值 */

#detailTable .form-table th:nth-child(10),
#detailTable .form-table td:nth-child(10) {
    width: 100px !important;
    min-width: 80px !important;
    max-width: 120px !important;
} /* JUDGEMENT - 判定结果 */

/* 调试辅助 - 为明细表列添加边框以验证列宽 */
#detailTable .form-table th,
#detailTable .form-table td {
    border: 2px solid #ff0000 !important; /* 临时红色边框，便于调试 */
    box-sizing: border-box !important;
}

/* 数字输入框样式 */
.form-table input[type="number"] {
    text-align: right;
}

.form-table input[type="date"] {
    text-align: center;
}

/* 必填字段标识 */
.form-table th.required::after {
    content: " *";
    color: #ff4d4f;
    font-weight: bold;
}

/* 表格容器滚动优化 */
.inspection-master-form,
.inspection-detail-form {
    overflow-x: auto;
    margin-bottom: 20px;
}

.inspection-master-form::-webkit-scrollbar,
.inspection-detail-form::-webkit-scrollbar {
    height: 6px;
}

.inspection-master-form::-webkit-scrollbar-track,
.inspection-detail-form::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.inspection-master-form::-webkit-scrollbar-thumb,
.inspection-detail-form::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.inspection-master-form::-webkit-scrollbar-thumb:hover,
.inspection-detail-form::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 点检明细表输入框样式 */
.detail-input {
    width: calc(100% - 4px);
    max-width: 100%;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 13px;
    line-height: 1.5;
    transition: all 0.3s ease;
    box-sizing: border-box;
    margin: 0;
    text-align: center; /* 所有输入框中文字居中对齐 */

    /* textarea特有样式 */
    resize: none; /* 禁止手动调整大小 */
    min-height: 40px; /* 最小高度 */
    max-height: 120px; /* 最大高度 */
    overflow-y: auto; /* 超出最大高度时显示滚动条 */
    white-space: pre-wrap; /* 保留换行符并自动换行 */
    word-wrap: break-word; /* 长单词自动换行 */
    word-break: break-all; /* 强制换行 */
    vertical-align: top; /* 顶部对齐 */
    font-family: inherit; /* 继承字体 */
}

/* 不可编辑输入框样式 */
.detail-input.readonly-input {
    border: 1px solid #ffffff; /* 边框颜色白色 */
    background-color: #ffffff;
    color: #333;
    cursor: default;
}

.detail-input.readonly-input:focus {
    border: 1px solid #ffffff;
    outline: none;
    box-shadow: none;
    background-color: #ffffff;
}

.detail-input.readonly-input:hover {
    border: 1px solid #ffffff;
    background-color: #ffffff;
}

/* 可编辑输入框样式 */
.detail-input.editable-input {
    border: 1px solid #1890ff; /* 边框颜色深蓝色 */
    background-color: #e6f7ff; /* 背景色淡蓝色 */
    color: #333;
}

.detail-input.editable-input:focus {
    border: 1px solid #1890ff; /* hover及focus样式不变 */
    outline: none;
    box-shadow: none;
    background-color: #e6f7ff;
}

.detail-input.editable-input:hover {
    border: 1px solid #1890ff; /* hover及focus样式不变 */
    background-color: #e6f7ff;
}

/* 表格单元格样式调整，适配textarea */
.form-table td {
    vertical-align: top; /* 顶部对齐 */
    padding: 8px 4px; /* 调整内边距 */
    height: auto; /* 允许高度自动调整 */
}

/* 表格行样式调整 */
.form-table tr {
    height: auto; /* 允许行高度自动调整 */
}

/* 明细表特殊样式调整 */
#detailTable .form-table td {
    min-height: 50px; /* 最小高度确保美观 */
}

/* 自动调整textarea高度的JavaScript辅助样式 */
.detail-input.auto-resize {
    height: auto;
}

/* 滚动条样式优化（针对textarea） */
.detail-input::-webkit-scrollbar {
    width: 4px;
}

.detail-input::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.detail-input::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 2px;
}

.detail-input::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 明细表容器滚动优化 */
.inspection-detail-form {
    overflow-x: auto; /* 水平滚动 */
    overflow-y: visible; /* 垂直方向不裁剪 */
    max-width: 100%;
}

/* 明细表最小宽度确保所有列都能显示 */
#detailTable .form-table {
    min-width: 1400px; /* 所有列的最小宽度总和 */
}

/* 响应式设计 - 中等屏幕 */
@media screen and (max-width: 1400px) {
    #detailTable .form-table th:nth-child(5),
    #detailTable .form-table td:nth-child(5) {
        width: 250px;
        min-width: 200px;
        max-width: 300px;
    } /* SPEC_TAR - 适当缩小 */

    #detailTable .form-table th:nth-child(2),
    #detailTable .form-table td:nth-child(2) {
        width: 180px;
        min-width: 150px;
        max-width: 200px;
    } /* CHECK_CONTENT - 适当缩小 */
}

/* 响应式设计 - 小屏幕 */
@media screen and (max-width: 1200px) {
    #detailTable .form-table {
        min-width: 1200px; /* 减少最小宽度 */
    }

    #detailTable .form-table th:nth-child(5),
    #detailTable .form-table td:nth-child(5) {
        width: 200px;
        min-width: 180px;
        max-width: 250px;
    } /* SPEC_TAR - 进一步缩小 */

    #detailTable .form-table th:nth-child(2),
    #detailTable .form-table td:nth-child(2) {
        width: 150px;
        min-width: 130px;
        max-width: 180px;
    } /* CHECK_CONTENT - 进一步缩小 */
}


