# updateDetailRows() 函数修改说明

## 修改概述
根据需求修改了 `updateDetailRows()` 函数，实现了根据不同项目类型生成预定义检查项目行数据的功能。

## 主要修改内容

### 1. 创建配置文件 (`checkItemsConfig.js`)
- **位置**: `ModelChange/checkItemsConfig.js`
- **功能**: 集中管理CP和OLB项目的预定义数据
- **结构**: 
  - CP项目：4行预定义数据
  - OLB项目：30行预定义数据
  - 每行包含：itemNo, checkContent, method, specLsl, specTar, specUsl, readonly字段

### 2. 重构 `updateDetailRows()` 函数
- **位置**: `ModelChange/model_change_history.js` (第386-428行)
- **改进**:
  - 使用配置文件数据，提高可维护性
  - 添加 `generateDetailRows()` 通用函数
  - 支持CP项目4行、OLB项目30行、默认显示"请选择工程"

### 3. 新增CSS样式
- **位置**: `ModelChange/ModelChange.css` (第710-762行)
- **功能**: 
  - 所有输入框文字居中对齐
  - 不可编辑字段：白色边框
  - 可编辑字段：深蓝色边框，淡蓝色背景
  - hover和focus样式保持一致

### 4. 更新HTML引用
- **位置**: `ModelChange/ModelChange.html` (第14行)
- **修改**: 添加了 `checkItemsConfig.js` 的引用

### 5. 修复提交函数
- **位置**: `ModelChange/model_change_history.js` (第470-496行)
- **修改**: 更新了数据收集逻辑，适配新的CSS类名结构

## 字段编辑权限

### CP项目 (4行)
- **不可编辑字段**: item-no, CHECK_CONTENT, METHOD, SPEC_LSL, SPEC_TAR, SPEC_USL, PIC_OK, PIC_NG
- **可编辑字段**: MEASURE, JUDGEMENT

### OLB项目 (30行)
- **不可编辑字段**: item-no, CHECK_CONTENT, METHOD, SPEC_LSL, SPEC_TAR, SPEC_USL, PIC_OK, PIC_NG
- **可编辑字段**: MEASURE, JUDGEMENT

### 统一编辑权限
所有项目类型现在都采用统一的编辑权限设置，简化了用户操作和系统维护。

## 技术实现特点

### 1. 数据与逻辑分离
- 预定义数据存储在独立配置文件中
- 业务逻辑与数据配置解耦，便于维护

### 2. 通用函数设计
- `generateDetailRows()` 函数可处理任意项目类型
- 支持灵活的字段编辑权限配置

### 3. 样式优化
- 使用CSS类区分可编辑和不可编辑字段
- 统一的视觉反馈，提升用户体验

## 文件变更清单

### 修改的文件
- `ModelChange/model_change_history.js` - 重构主要函数
- `ModelChange/ModelChange.css` - 新增输入框样式
- `ModelChange/ModelChange.html` - 添加配置文件引用

### 新增的文件
- `ModelChange/checkItemsConfig.js` - 项目配置数据

## 测试验证

### 功能测试
1. 选择CP项目 → 显示4行预定义数据
2. 选择OLB项目 → 显示30行预定义数据
3. 选择"请选择" → 显示"请选择工程"提示

### 样式测试
1. 不可编辑字段：白色边框，正常背景
2. 可编辑字段：深蓝色边框，淡蓝色背景
3. 所有输入框文字居中对齐

### 数据提交测试
1. 验证数据收集逻辑正确
2. 确保提交时跳过提示行
3. 检查字段映射准确性

## 后续维护

### 添加新项目类型
1. 在 `checkItemsConfig.js` 中添加新的项目配置
2. 在 `updateDetailRows()` 函数中添加对应的条件分支

### 修改现有配置
1. 直接编辑 `checkItemsConfig.js` 文件
2. 无需修改业务逻辑代码

### 样式调整
1. 修改 `ModelChange.css` 中的 `.detail-input` 相关样式
2. 可独立调整可编辑和不可编辑字段的视觉效果
