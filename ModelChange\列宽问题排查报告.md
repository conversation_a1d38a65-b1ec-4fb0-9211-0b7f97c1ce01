# 点检明细表列宽控制问题排查报告

## 问题描述
在点检登录模态框中，明细表（detailTable）的各列宽度显示一致，没有按照CSS中设置的独立列宽生效。

## 问题原因分析

### 1. CSS优先级冲突
**问题**：通用的 `.form-table` 规则与特定的 `#detailTable` 规则产生冲突
**具体冲突**：
- `.form-table td { min-width: 80px; }` 覆盖了特定列宽设置
- `.form-table th:nth-child(n)` 通用列宽规则与明细表列宽冲突

### 2. 表格布局模式问题
**问题**：`table-layout: fixed` 可能没有正确应用
**影响**：浏览器使用自动布局算法，忽略了CSS中的宽度设置

### 3. CSS选择器优先级不足
**问题**：没有使用 `!important` 确保关键样式的优先级

## 修复措施

### 1. 移除冲突的通用样式
```css
.form-table td {
    background-color: #fff;
    height: 50px;
    /* min-width: 80px; 移除通用最小宽度，避免与明细表列宽冲突 */
}
```

### 2. 增强CSS选择器优先级
```css
#detailTable {
    table-layout: fixed !important;
    width: 100% !important;
}

#detailTable .form-table {
    table-layout: fixed !important;
    width: 100% !important;
}
```

### 3. 为所有列宽设置添加 !important
```css
#detailTable .form-table th:nth-child(1), 
#detailTable .form-table td:nth-child(1) { 
    width: 80px !important; 
    min-width: 80px !important; 
    max-width: 80px !important; 
}
```

### 4. 排除明细表的通用规则
```css
.form-table:not(#detailTable .form-table) th:nth-child(1), 
.form-table:not(#detailTable .form-table) td:nth-child(1) { 
    min-width: 80px; 
}
```

### 5. 添加调试辅助
```css
/* 临时调试边框 */
#detailTable .form-table th,
#detailTable .form-table td {
    border: 2px solid #ff0000 !important;
    box-sizing: border-box !important;
}
```

```javascript
// JavaScript调试函数
function debugTableLayout() {
    const table = document.querySelector('#detailTable .form-table');
    if (table) {
        console.log('表格布局模式:', window.getComputedStyle(table).tableLayout);
        // 输出各列的计算样式
    }
}
```

## 预期列宽分配

| 列名 | 宽度 | 说明 |
|------|------|------|
| ITEM_NO | 80px | 序号列，固定宽度 |
| CHECK_CONTENT | 200px | 检查内容，需要较宽空间 |
| METHOD | 120px | 检查方法 |
| SPEC_LSL | 100px | 规格下限 |
| **SPEC_TAR** | **300px** | **规格目标值，最宽列** |
| SPEC_USL | 100px | 规格上限 |
| PIC_OK | 100px | 合格图片 |
| PIC_NG | 100px | 不合格图片 |
| MEASURE | 120px | 测量值（可编辑） |
| JUDGEMENT | 100px | 判定结果（可编辑） |

## 验证步骤

### 1. 视觉验证
1. 打开点检登录模态框
2. 选择OLB项目
3. 检查是否显示红色调试边框
4. 观察各列宽度是否明显不同
5. 特别关注SPEC_TAR列是否最宽

### 2. 开发者工具验证
1. 按F12打开开发者工具
2. 选择Elements标签
3. 找到 `#detailTable .form-table`
4. 检查Computed样式中的：
   - `table-layout: fixed`
   - 各列的 `width`、`min-width`、`max-width`

### 3. 控制台验证
1. 打开Console标签
2. 查看调试输出信息
3. 验证各列的计算样式值

## 后续清理

如果列宽正确显示，需要移除调试代码：

```css
/* 移除这段调试代码 */
#detailTable .form-table th,
#detailTable .form-table td {
    border: 2px solid #ff0000 !important;
    box-sizing: border-box !important;
}
```

```javascript
// 移除或注释调试函数调用
// debugTableLayout();
```

## 可能的其他问题

### 1. 浏览器兼容性
- 确认浏览器支持 `table-layout: fixed`
- 检查CSS Grid或Flexbox是否更适合

### 2. 动态内容影响
- 确认JavaScript没有动态修改表格样式
- 检查是否有其他CSS框架覆盖样式

### 3. 响应式设计冲突
- 检查媒体查询是否影响列宽
- 确认viewport设置正确

## 总结

通过系统性地排查CSS优先级冲突、表格布局模式和选择器特异性问题，应该能够解决列宽控制不生效的问题。关键是确保 `table-layout: fixed` 正确应用，并且没有其他CSS规则覆盖我们的列宽设置。
